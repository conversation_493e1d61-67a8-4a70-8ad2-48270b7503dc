"use client";

import { useEffect } from "react";

export default function Analytics() {
  useEffect(() => {
    // Web Vitals tracking
    if (typeof window !== "undefined" && "performance" in window) {
      // Track Core Web Vitals
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === "navigation") {
            const navEntry = entry as PerformanceNavigationTiming;
            console.log("Page Load Time:", navEntry.loadEventEnd - navEntry.loadEventStart);
          }
          
          if (entry.entryType === "paint") {
            console.log(`${entry.name}:`, entry.startTime);
          }
        }
      });

      observer.observe({ entryTypes: ["navigation", "paint"] });

      // Track Largest Contentful Paint (LCP)
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        console.log("LCP:", lastEntry.startTime);
      });

      lcpObserver.observe({ entryTypes: ["largest-contentful-paint"] });

      // Track First Input Delay (FID)
      const fidObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          const fidEntry = entry as any; // Type assertion for FID entry
          if (fidEntry.processingStart) {
            const fid = fidEntry.processingStart - entry.startTime;
            console.log("FID:", fid);
          }
        }
      });

      fidObserver.observe({ entryTypes: ["first-input"] });

      // Track Cumulative Layout Shift (CLS)
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
          }
        }
        console.log("CLS:", clsValue);
      });

      clsObserver.observe({ entryTypes: ["layout-shift"] });

      return () => {
        observer.disconnect();
        lcpObserver.disconnect();
        fidObserver.disconnect();
        clsObserver.disconnect();
      };
    }
  }, []);

  useEffect(() => {
    // Track user interactions for conversion optimization
    const trackInteraction = (event: string, element?: string) => {
      console.log(`User interaction: ${event}`, element);
      // Here you would send to your analytics service
    };

    // Track scroll depth
    let maxScroll = 0;
    const handleScroll = () => {
      const scrollPercent = Math.round(
        (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100
      );
      
      if (scrollPercent > maxScroll) {
        maxScroll = scrollPercent;
        
        // Track milestone scrolls
        if ([25, 50, 75, 90].includes(scrollPercent)) {
          trackInteraction("scroll_depth", `${scrollPercent}%`);
        }
      }
    };

    // Track CTA clicks
    const handleCTAClick = (event: Event) => {
      const target = event.target as HTMLElement;
      if (target.textContent?.includes("Book") || target.textContent?.includes("WhatsApp")) {
        trackInteraction("cta_click", target.textContent);
      }
    };

    // Track section views
    const sectionObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const sectionId = entry.target.id || entry.target.className;
            trackInteraction("section_view", sectionId);
          }
        });
      },
      { threshold: 0.5 }
    );

    // Observe sections
    const sections = document.querySelectorAll("section[id]");
    sections.forEach((section) => sectionObserver.observe(section));

    window.addEventListener("scroll", handleScroll);
    document.addEventListener("click", handleCTAClick);

    return () => {
      window.removeEventListener("scroll", handleScroll);
      document.removeEventListener("click", handleCTAClick);
      sectionObserver.disconnect();
    };
  }, []);

  return null; // This component doesn't render anything
}
